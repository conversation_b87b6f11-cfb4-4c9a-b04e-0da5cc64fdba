<template>
  <div class="panel-section map-container">
    <div class="map-title">
      <span style="width: 3px; height: 16px; background-color: #2997e3; margin-right: 8px;"></span>
      地图
    </div>
    <div class="map-content">
      <!-- 地图控制按钮 -->
      <div class="map-controls">
        <button class="map-btn"
          :class="{ active: heatmapVisible }"
          @click="toggleHeatmap"
        >
          人员热力图
          <span v-if="heatmapVisible" class="ws-status" :class="{ connected: wsConnected }">
            {{ wsConnected ? '●' : '○' }}
          </span>
        </button>
        <button class="map-btn">引导员位置</button>
        <button class="map-btn"
          :class="{ active: networkVisible }"
          @click="toggleNetwork"
        >网络图</button>
        <button class="map-btn">疏散路线</button>
        <button class="map-btn">指示标识</button>
        <button class="map-btn"
          :class="{ active: gridLayerState === 1, only: gridLayerState === 2 }"
          @click="toggleGridLayer"
        >网格图</button>
      </div>

      <!-- 地图显示区域 -->
      <div class="map-viewport">
        <div ref="threeContainer" class="three-map-canvas">
          <!-- 地图选择器 -->
          <div class="map-selector">
            <select v-model="selectedMap" @change="changeMap" class="map-select">
              <option value="map_精简版.json">map_精简版</option>
              <option value="管理学院_添加grid后.json">管理学院_添加grid后</option>
            </select>
          </div>
          <!-- 左侧指南针 -->
          <div class="compass" :style="compassStyle">
            <svg width="36" height="36" viewBox="0 0 36 36">
              <circle cx="18" cy="18" r="17" fill="#fff" stroke="#3553b0" stroke-width="2"/>
              <polygon points="18,9 22,22 18,16 14,22" fill="#e74c3c"/>
              <text x="18" y="11" text-anchor="middle" font-size="10" fill="#3553b0" font-weight="bold">N</text>
            </svg>
          </div>
          <!-- 左下角比例尺 -->
          <div class="map-scale">
            <div class="scale-bar-wrapper" :style="{ width: scaleBarPx + 'px' }">
              <div class="scale-bar-tip" style="left:0"></div>
              <div class="scale-bar-tip" :style="{ left: (scaleBarPx - 2) + 'px' }"></div>
              <div class="scale-bar" :style="{ width: scaleBarPx + 'px' }"></div>
            </div>
            <span class="scale-label">{{ scaleBarMeter }}米</span>
          </div>
        </div>
      </div>

      <!-- 地图右侧控制 -->
      <div class="map-right-controls">
        <div class="mode-control">
          <div class="mode-btn" @click="toggleNewLayer">
            <span class="icon-3d">{{ newLayerVisible ? '隐' : '显' }}</span>
          </div>
        </div>
        <div class="floor-controls">
          <button class="floor-btn floor-icon-btn">
            <span class="floor-icon">楼</span>
          </button>
          <button v-for="(floor, idx) in floors" :key="floor.id"
            :class="['floor-btn', idx === currentFloorIdx ? 'active' : '']"
            @click="switchFloor(idx)">
            {{ idx === 0 ? '1F' : '2F' }}
          </button>
        </div>
        <div class="zoom-controls">
          <button class="zoom-btn" @click="zoomIn"></button>
          <button class="zoom-btn zoom-out" @click="zoomOut"></button>
          <button class="zoom-btn" @click="toggleSingleLayer" title="单/多层">{{ singleLayerMode ? '多' : '单' }}</button>
          <button class="zoom-btn" @click="setTopView" title="俯视">{{ isTopView ? '原' : '俯' }}</button>
        </div>
      </div>
    </div>

    <!-- 悬浮提示 -->
    <div v-if="hoveredVertexId" :style="{ position: 'fixed', left: hoveredScreen.x + 12 + 'px', top: hoveredScreen.y + 12 + 'px', background: 'rgba(0,0,0,0.8)', color: '#fff', padding: '4px 8px', borderRadius: '4px', pointerEvents: 'none', zIndex: 9999, fontSize: '14px' }">
      点ID: {{ hoveredVertexId }}
    </div>
    <div v-if="hoveredBlockName" :style="{ position: 'fixed', left: hoveredBlockScreen.x + 12 + 'px', top: hoveredBlockScreen.y + 12 + 'px', background: 'rgba(0,0,0,0.8)', color: '#fff', padding: '4px 8px', borderRadius: '4px', pointerEvents: 'none', zIndex: 9999, fontSize: '14px' }">
      点名称: {{ hoveredBlockName }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, computed } from 'vue';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import axios from 'axios';

// 导入所有地图数据
import mapDataRaw from './map/map_精简版.json';
import managementGridRaw from './map/管理学院_添加grid后.json';

// 地图数据映射
const mapDataMap = {
  'map_精简版.json': mapDataRaw.map,
  '管理学院_添加grid后.json': managementGridRaw.map
};

const selectedMap = ref('map_精简版.json');
const mapData = ref(mapDataMap[selectedMap.value as keyof typeof mapDataMap]);

// 暴露事件给父组件
const emit = defineEmits([
  'floor-changed',
  'zoom-changed',
  'map-control-changed',
  'update:mapTitle'
]);

// 可以通过props接收配置
const props = defineProps({
  initialFloor: {
    type: String,
    default: 'B1'
  },
  initialScale: {
    type: Number,
    default: 1
  },
  enableDrag: {
    type: Boolean,
    default: true
  }
});

const mapTitle = ref(mapData.value.building?.name || '');
const threeContainer = ref<HTMLDivElement | null>(null);
let renderer: THREE.WebGLRenderer | null = null;
let scene: THREE.Scene | null = null;
let camera: THREE.PerspectiveCamera | null = null;
let controls: OrbitControls | null = null;
let animationId: number | null = null;

const floors = computed(() => mapData.value.building.floors);
const currentFloorIdx = ref(0);
const scaleBarPx = ref(80); // 比例尺像素宽度固定
const scaleBarMeter = ref(5); // 比例尺代表的米数，初始为5
const compassAngle = ref(0); // 指南针角度
const FLOOR_GAP = 15; // 增大层间隔，使视角变化更明显
const singleLayerMode = ref(false); // 单层/多层模式
const newLayerVisible = ref(false); // 新图层可见状态
const gridLayerState = ref(0); // 0:未开, 1:叠加, 2:仅网格
const heatmapVisible = ref(false); // 热力图可见状态
const networkVisible = ref(false); // 网络图可见状态

// WebSocket 热力图相关状态
const websocket = ref<WebSocket | null>(null);
const heatmapData = ref<any>(null); // 存储实时热力图数据
const wsConnected = ref(false); // WebSocket连接状态
let heatmapUpdateTimer: number | null = null; // 防抖定时器

const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2();
const hoveredVertexId = ref<string | null>(null);
const hoveredScreen = ref({ x: 0, y: 0 });
const hoveredBlockName = ref<string|null>(null);
const hoveredBlockScreen = ref<{x:number,y:number}>({x:0,y:0});
let vertexMeshes: THREE.Mesh[] = [];

const isTopView = ref(false);
const originalCameraPos = ref<{x:number,y:number,z:number}|null>(null);
const originalTarget = ref<{x:number,y:number,z:number}|null>(null);

// 判断点是否在多边形内（射线法）
function isPointInPolygon(point: [number, number], polygon: [number, number][]): boolean {
  const [x, y] = point;
  let inside = false;
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const [xi, yi] = polygon[i];
    const [xj, yj] = polygon[j];
    const intersect = ((yi > y) !== (yj > y)) &&
      (x < (xj - xi) * (y - yi) / (yj - yi + 1e-10) + xi);
    if (intersect) inside = !inside;
  }
  return inside;
}

// 地图切换函数
function changeMap() {
  removeHeatmapMeshes();
  removeNetworkMeshes();
  mapData.value = mapDataMap[selectedMap.value as keyof typeof mapDataMap];
  mapTitle.value = mapData.value.building?.name || '';
  currentFloorIdx.value = 0;
  // 只有当mapTitle有值时才发送更新事件
  if (mapTitle.value) {
    emit('update:mapTitle', mapTitle.value);
  }
  drawMap();
}

function toggleSingleLayer() {
  singleLayerMode.value = !singleLayerMode.value;
  drawMap();
}

function toggleNewLayer() {
  newLayerVisible.value = !newLayerVisible.value;
  drawMap();
}

function toggleGridLayer() {
  gridLayerState.value = (gridLayerState.value + 1) % 3;
  drawMap();
}

function toggleHeatmap() {
  heatmapVisible.value = !heatmapVisible.value;
  emit('map-control-changed', {
    control: '人员热力图',
    active: heatmapVisible.value
  });

  // 如果开启热力图，连接WebSocket；如果关闭，断开连接
  if (heatmapVisible.value) {
    connectWebSocket();
  } else {
    disconnectWebSocket();
    // 移除热力图网格
    removeHeatmapMeshes();
  }

  // 无论开启还是关闭热力图，都重新绘制地图以更新建筑颜色
  drawMap();
}

function toggleNetwork() {
  networkVisible.value = !networkVisible.value;
  emit('map-control-changed', {
    control: '网络图',
    active: networkVisible.value
  });
  drawMap();
}

// WebSocket 连接函数
function connectWebSocket() {
  if (websocket.value) {
    websocket.value.close();
  }

  try {
    websocket.value = new WebSocket('ws://139.196.23.0:8765/ws');

    websocket.value.onopen = () => {
      console.log('热力图WebSocket连接成功');
      wsConnected.value = true;
    };

    websocket.value.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        if (message.type === 'heatmap_update') {
          heatmapData.value = message.payload;
          // 防抖更新：避免过于频繁的渲染
          if (heatmapVisible.value) {
            if (heatmapUpdateTimer) {
              clearTimeout(heatmapUpdateTimer);
            }
            heatmapUpdateTimer = setTimeout(() => {
              updateHeatmapOnly();
              heatmapUpdateTimer = null;
            }, 100); // 100ms防抖延迟
          }
        }
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    };

    websocket.value.onerror = (error) => {
      console.error('WebSocket连接错误:', error);
      wsConnected.value = false;
    };

    websocket.value.onclose = () => {
      console.log('WebSocket连接已关闭');
      wsConnected.value = false;
    };

  } catch (error) {
    console.error('创建WebSocket连接失败:', error);
    wsConnected.value = false;
  }
}

// WebSocket 断开函数
function disconnectWebSocket() {
  if (websocket.value) {
    websocket.value.close();
    websocket.value = null;
  }
  wsConnected.value = false;
  heatmapData.value = null;

  // 清理防抖定时器
  if (heatmapUpdateTimer) {
    clearTimeout(heatmapUpdateTimer);
    heatmapUpdateTimer = null;
  }
}

const compassStyle = computed(() => ({
  transform: `rotate(${-compassAngle.value}deg)` ,
  right: '16px',
  bottom: '16px',
  position: 'absolute' as const,
  width: '36px',
  height: '36px',
  zIndex: '10',
  transition: 'transform 0.2s',
  userSelect: 'none' as const,
}));


function zoomIn() {
  if (camera) {
    camera.zoom = Math.min(camera.zoom + 0.2, 3);
    camera.updateProjectionMatrix();
    emit('zoom-changed', camera.zoom);
  }
}
function zoomOut() {
  if (camera) {
    camera.zoom = Math.max(camera.zoom - 0.2, 0.5);
    camera.updateProjectionMatrix();
    emit('zoom-changed', camera.zoom);
  }
}
function getGlobalCenter() {
  let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;
  for (const floor of floors.value) {
    for (const block of floor.blocks || []) {
      // 类型检查：确保block有points属性
      if ('points' in block && Array.isArray(block.points)) {
        for (const [x, y] of block.points as [number, number][]) {
          minX = Math.min(minX, x);
          maxX = Math.max(maxX, x);
          minY = Math.min(minY, y);
          maxY = Math.max(maxY, y);
        }
      }
    }
  }
  const centerX = (minX + maxX) / 2;
  const centerY = (minY + maxY) / 2;
  return { centerX, centerY, minX, maxX, minY, maxY };
}
function switchFloor(idx: number) {
  removeHeatmapMeshes();
  removeNetworkMeshes();
  currentFloorIdx.value = idx;
  const floorName = idx === 0 ? 'B1' : (idx === 1 ? 'M' : 'B2');
  emit('floor-changed', floorName);
  drawMap();
}

function animate() {
  if (scene && camera && renderer && controls) {
    controls.update();
    renderer.render(scene, camera);
    updateScaleBar();
    compassAngle.value = THREE.MathUtils.radToDeg(controls.getAzimuthalAngle());
  }
  animationId = requestAnimationFrame(animate);
}

// 确保相机和控制器状态一致
function ensureCameraConsistency() {
  if (!camera || !controls) return;
  const { centerX, centerY } = getGlobalCenter();
  // 确保控制器的目标点与相机lookAt的目标一致
  controls.target.set(centerX, centerY, currentFloorIdx.value * FLOOR_GAP);
  controls.update();
}

// 比例尺动态长度与数值
const scaleBarScales = [0.5,1, 2, 5, 10, 20, 50, 100, 200, 500, 1000];
const minScaleBarPx = 60; // 最短像素
const maxScaleBarPx = 120; // 最长像素
function updateScaleBar() {
  if (!camera || !renderer) return;
  const size = new THREE.Vector2();
  renderer.getSize(size);
  const width = size.x;
  // 取画布中心下方一段
  const worldStart = new THREE.Vector3(0, 0, 0);
  const worldEnd = new THREE.Vector3(5, 0, 0); // 5米
  worldStart.project(camera);
  worldEnd.project(camera);
  const px1 = (worldStart.x + 1) / 2 * width;
  const px2 = (worldEnd.x + 1) / 2 * width;
  const pxPer5m = Math.abs(px2 - px1);
  // 以5米为基准，动态选择合适的刻度
  let chosenMeter = 5;
  let chosenPx = pxPer5m;
  for (const meter of scaleBarScales) {
    const worldEnd = new THREE.Vector3(meter, 0, 0).project(camera);
    const px2 = (worldEnd.x + 1) / 2 * width;
    const pxLen = Math.abs(px2 - px1);
    if (pxLen >= minScaleBarPx && pxLen <= maxScaleBarPx) {
      chosenMeter = meter;
      chosenPx = pxLen;
      break;
    }
  }
  scaleBarMeter.value = chosenMeter;
  scaleBarPx.value = chosenPx;
}

watch(() => camera?.zoom, () => {
  updateScaleBar();
});

watch(currentFloorIdx, (newVal) => {
  if (camera) {
    const { centerX, centerY, minX, maxX, minY, maxY } = getGlobalCenter();
    const mapSize = Math.max(maxX - minX, maxY - minY);
    const initialZ = mapSize * 1.2;
    camera.position.set(centerX, centerY - initialZ*0.8, initialZ*0.4 + newVal * FLOOR_GAP); // Y轴偏移=Z轴高度，形成45度角
    camera.up.set(0, 0, 1); // 确保up向量正确设置
    camera.lookAt(centerX, centerY, newVal * FLOOR_GAP); // 统一目标点z坐标，修复视角倾斜
    camera.updateProjectionMatrix();
    // 同时更新控制器的目标点
    if (controls) {
      controls.target.set(centerX, centerY, newVal * FLOOR_GAP);
      controls.update();
    }
  }
});

watch([currentFloorIdx, floors], () => {
  let maxBlockHeight = 0;
  const blocks = floors.value[currentFloorIdx.value]?.blocks || [];
  for (const block of blocks) {
    if (typeof block.height === 'number') {
      maxBlockHeight = Math.max(maxBlockHeight, block.height);
    }
  }
}, { immediate: true });

function drawMap() {
  removeHeatmapMeshes();
  removeNetworkMeshes();
  console.log('开始重新绘制地图');
  if (!threeContainer.value) return;
  // 销毁旧控制器
  if (controls) {
    controls.dispose();
    controls = null;
  }
  if (renderer && renderer.domElement && renderer.domElement.parentNode) {
    renderer.domElement.parentNode.removeChild(renderer.domElement);
  }
  renderer = new THREE.WebGLRenderer({ antialias: true });
  const container = threeContainer.value;
  const width = container.clientWidth;
  const height = container.clientHeight;
  renderer.setSize(width, height);
  container.appendChild(renderer.domElement);
  scene = new THREE.Scene();
  scene.background = new THREE.Color('#0d2b55');
  if (!camera) {
    const { centerX, centerY, minX, maxX, minY, maxY } = getGlobalCenter();
    const mapWidth = maxX - minX;
    const mapHeight = maxY - minY;
    const mapSize = Math.max(mapWidth, mapHeight);
    camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
    const initialZ = mapSize * 1.2;
    camera.position.set(centerX, centerY - initialZ*0.8, initialZ*0.4 + currentFloorIdx.value * FLOOR_GAP); // 与watch函数保持一致
    camera.up.set(0, 0, 1); // 确保up向量正确设置
    camera.lookAt(centerX, centerY, currentFloorIdx.value * FLOOR_GAP); // 与watch函数保持一致
    camera.zoom = 1.2;
    camera.updateProjectionMatrix();
  }

  // 获取地图中心点和范围
  getGlobalCenter();

  // 网格平面模式：渲染网格和其他激活的图层
  if (gridLayerState.value === 2) {
    if (!controls) {
      controls = new OrbitControls(camera!, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.1;
      controls.screenSpacePanning = true;
      controls.enableRotate = false;
      controls.enablePan = true;
      controls.mouseButtons.LEFT = THREE.MOUSE.PAN;
      controls.minDistance = 10;
      controls.maxDistance = 2000;
      controls.maxPolarAngle = Math.PI;
      controls.enableZoom = true;
      // 设置初始目标点
      const { centerX, centerY } = getGlobalCenter();
      controls.target.set(centerX, centerY, currentFloorIdx.value * FLOOR_GAP);
    } else {
      // 类型断言确保 controls 不为 null
      const currentControls = controls as OrbitControls;
      currentControls.object = camera!;
      currentControls.domElement = renderer.domElement;
      currentControls.enableZoom = true;
      // 确保目标点正确设置
      const { centerX, centerY } = getGlobalCenter();
      currentControls.target.set(centerX, centerY, currentFloorIdx.value * FLOOR_GAP);
    }
    controls.update();

        // 渲染网格
    renderGridLayer();

    // 渲染其他激活的图层
    if (networkVisible.value) {
      renderNetworkLayer();
    }

    if (heatmapVisible.value) {
      renderHeatmapLayer();
    }

    if (animationId) cancelAnimationFrame(animationId);
    ensureCameraConsistency();
    animate();
    return;
  }

  // 光源
  const light = new THREE.DirectionalLight(0xffffff, 1);
  light.position.set(0, 0, 50);
  scene.add(light);
  scene.add(new THREE.AmbientLight(0xffffff, 0.5));
  // controls
  if (!controls) {
    controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.1;
    controls.screenSpacePanning = true;
    controls.enableRotate = false;
    controls.enablePan = true;
    controls.mouseButtons.LEFT = THREE.MOUSE.PAN;
    controls.minDistance = 10;
    controls.maxDistance = 2000;
    controls.maxPolarAngle = Math.PI;
    controls.enableZoom = true;
    // 设置初始目标点
    const { centerX, centerY } = getGlobalCenter();
    controls.target.set(centerX, centerY, currentFloorIdx.value * FLOOR_GAP);
  } else {
    // 类型断言确保 controls 不为 null
    const currentControls = controls as OrbitControls;
    currentControls.object = camera; // 更新控制器关联的相机
    currentControls.domElement = renderer.domElement; // 更新DOM元素
    // 确保目标点正确设置
    const { centerX, centerY } = getGlobalCenter();
    currentControls.target.set(centerX, centerY, currentFloorIdx.value * FLOOR_GAP);
    currentControls.update();
  }
  // 多层渲染，zOffset逐层递增
  let zOffset = 0;
  vertexMeshes = [];
  for (let i = 0; i < floors.value.length; i++) {
    if (singleLayerMode.value && i !== currentFloorIdx.value) continue;
    const floor = floors.value[i];
    const isCurrent = i === currentFloorIdx.value;
    // 原有绘制逻辑
    for (const block of floor.blocks || []) {
      // 类型检查：确保block有points属性
      if ('points' in block && Array.isArray(block.points)) {
        const pointsArr = block.points as [number, number][];
        const shape = new THREE.Shape();
        pointsArr.forEach(([x, y], idx) => {
          if (idx === 0) shape.moveTo(x, y);
          else shape.lineTo(x, y);
        });
        const blockHeight = 'height' in block && typeof block.height === 'number' ? block.height : 1;
        // 当热力图可见时，所有建筑统一使用白色；否则使用原始颜色
        const blockColor = heatmapVisible.value ? 0xffffff : ('rgb' in block && typeof block.rgb === 'number' ? block.rgb : 0x00ffff);
        const extrudeSettings = { depth: blockHeight, bevelEnabled: false };
        const material = new THREE.MeshLambertMaterial({ color: blockColor, transparent: true, opacity: isCurrent ? 0.9 : 0.5 });
        const mesh = new THREE.Mesh(new THREE.ExtrudeGeometry(shape, extrudeSettings), material);
        mesh.position.z = zOffset;
        // --- 新增：block与name的映射 ---
        if (isCurrent && Array.isArray(floor.vertices)) {
          const names: string[] = [];
          for (const v of floor.vertices) {
            if (v.name && Array.isArray(v.position) && v.position.length === 2 && isPointInPolygon([v.position[0], v.position[1]], pointsArr)) {
              names.push(v.name);
            }
          }
          if (names.length === 1) {
            mesh.userData.vertexName = names[0];
          } else if (names.length > 1) {
            mesh.userData.vertexName = names[0] + `（${names.length}）`;
          }
        }
        // ---
        scene!.add(mesh);
        // 顶部边框（淡紫色）
        const blockEdgePoints: THREE.Vector3[] = pointsArr.map(([x, y]) => new THREE.Vector3(x, y, zOffset + blockHeight + 0.01));
        if (pointsArr.length > 1) blockEdgePoints.push(new THREE.Vector3(pointsArr[0][0], pointsArr[0][1], zOffset + blockHeight + 0.01));
        const edgeGeometry = new THREE.BufferGeometry().setFromPoints(blockEdgePoints);
        const edgeMaterial = new THREE.LineBasicMaterial({ color: 0xb39ddb, linewidth: 2 });
        const edgeLine = new THREE.Line(edgeGeometry, edgeMaterial);
        scene!.add(edgeLine);
        // 底部边框（淡紫色）
        const bottomEdgePoints: THREE.Vector3[] = pointsArr.map(([x, y]) => new THREE.Vector3(x, y, zOffset));
        if (pointsArr.length > 1) bottomEdgePoints.push(new THREE.Vector3(pointsArr[0][0], pointsArr[0][1], zOffset));
        const bottomEdgeGeometry = new THREE.BufferGeometry().setFromPoints(bottomEdgePoints);
        const bottomEdgeLine = new THREE.Line(bottomEdgeGeometry, edgeMaterial);
        scene!.add(bottomEdgeLine);
        // 侧面边框（淡紫色）
        for (let j = 0; j < pointsArr.length; j++) {
          const [x, y] = pointsArr[j];
          const sideEdgeGeometry = new THREE.BufferGeometry().setFromPoints([
            new THREE.Vector3(x, y, zOffset),
            new THREE.Vector3(x, y, zOffset + blockHeight + 0.01)
          ]);
          const sideEdgeLine = new THREE.Line(sideEdgeGeometry, edgeMaterial);
          scene!.add(sideEdgeLine);
        }
      }
    }
    // 绘制新图层
    if (newLayerVisible.value) {
      // 绘制楼层多边形
      if ('points' in floor && Array.isArray(floor.points)) {
        const floorShape = new THREE.Shape();
        const floorPoints = floor.points as [number, number][];
        floorPoints.forEach(([x, y], idx) => {
          if (idx === 0) floorShape.moveTo(x, y);
          else floorShape.lineTo(x, y);
        });
        // 处理挖空区域
        if ('holes' in floor && Array.isArray(floor.holes)) {
          floor.holes.forEach((hole: [number, number][]) => {
            const holeShape = new THREE.Path();
            hole.forEach(([x, y], idx) => {
              if (idx === 0) holeShape.moveTo(x, y);
              else holeShape.lineTo(x, y);
            });
            floorShape.holes.push(holeShape);
          });
        }
        const floorExtrudeSettings = { depth: 0.1, bevelEnabled: false };
        const floorMaterial = new THREE.MeshLambertMaterial({ color: 0xffdab9, transparent: true, opacity: 0.9 });
        const floorMesh = new THREE.Mesh(new THREE.ExtrudeGeometry(floorShape, floorExtrudeSettings), floorMaterial);
        floorMesh.position.z = zOffset;
        scene!.add(floorMesh);

        // 生成墙面（每条边一个BufferGeometry矩形面）
        const wallHeight = 1;
        const wallMaterial = new THREE.MeshLambertMaterial({
          color: 0xffdab9,
          transparent: true,
          opacity: 0.9,
          side: THREE.DoubleSide
        });
        for (let i = 0; i < floorPoints.length; i++) {
          const [x1, y1] = floorPoints[i];
          const [x2, y2] = floorPoints[(i + 1) % floorPoints.length];
          const p0 = new THREE.Vector3(x1, y1, zOffset);
          const p1 = new THREE.Vector3(x2, y2, zOffset);
          const p2 = new THREE.Vector3(x2, y2, zOffset + wallHeight);
          const p3 = new THREE.Vector3(x1, y1, zOffset + wallHeight);
          const vertices = [
            p0.x, p0.y, p0.z,
            p1.x, p1.y, p1.z,
            p2.x, p2.y, p2.z,
            p3.x, p3.y, p3.z
          ];
          const indices = [0, 1, 2, 0, 2, 3];
          const geometry = new THREE.BufferGeometry();
          geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
          geometry.setIndex(indices);
          geometry.computeVertexNormals();
          const mesh = new THREE.Mesh(geometry, wallMaterial);
          scene!.add(mesh);
        }
      }
    }
    zOffset += FLOOR_GAP;
  }
  // 渲染网络图层
  if (networkVisible.value) {
    renderNetworkLayer();
  }
  // 渲染热力图图层
  if (heatmapVisible.value) {
    renderHeatmapLayer();
  }
  // 渲染grids图层（叠加模式）
  if (gridLayerState.value === 1) {
    renderGridLayer();
  }
  // 动画
  if (animationId) cancelAnimationFrame(animationId);
  ensureCameraConsistency();
  animate();
}

function renderNetworkLayer() {
  // 多层渲染，zOffset逐层递增
  let zOffset = 0;
  vertexMeshes = [];
  for (let i = 0; i < floors.value.length; i++) {
    if (singleLayerMode.value && i !== currentFloorIdx.value) continue;
    const floor = floors.value[i];

    // 绘制vertices
    if ('vertices' in floor && Array.isArray(floor.vertices)) {
      for (const v of floor.vertices) {
        const [x, y] = v.position as [number, number];
        const color = 0xff0000;
        const geo = new THREE.SphereGeometry(0.3, 16, 16);
        const mat = new THREE.MeshBasicMaterial({ color });
        const mesh = new THREE.Mesh(geo, mat);
        mesh.position.set(x, y, zOffset);
        mesh.userData.vertexId = v.id;
        mesh.name = 'network';
        scene!.add(mesh);
        vertexMeshes.push(mesh);
      }
    }
    // 绘制road和edges
    if ('vertices' in floor && Array.isArray(floor.vertices)) {
      const vertexMap = new Map<string, [number, number]>();
      for (const v of floor.vertices) {
        vertexMap.set(v.id, v.position as [number, number]);
      }
      // 1. road.edges（对象结构）
      const roadEdges = (floor as unknown as { road?: { edges?: object } })?.road?.edges;
      if (roadEdges && typeof roadEdges === 'object' && !Array.isArray(roadEdges)) {
        const edges = roadEdges;
        const drawn = new Set<string>();
        Object.entries(edges).forEach(([fromId, toIds]) => {
          if (Array.isArray(toIds)) {
            toIds.forEach((toId: string) => {
              const key = [fromId, toId].sort().join('->');
              if (drawn.has(key)) return;
              drawn.add(key);
              const fromV = vertexMap.get(fromId);
              const toV = vertexMap.get(toId);
              if (fromV && toV) {
                const color = 0x0000ff;
                const points = [
                  new THREE.Vector3(fromV[0], fromV[1], zOffset),
                  new THREE.Vector3(toV[0], toV[1], zOffset)
                ];
                const geometry = new THREE.BufferGeometry().setFromPoints(points);
                const material = new THREE.LineBasicMaterial({ color });
                const line = new THREE.Line(geometry, material);
                line.name = 'network';
                scene!.add(line);
              }
            });
          }
        });
      }
      // 2. edges（数组结构）
      const arrEdges = (floor as unknown as { edges?: unknown[] })?.edges;
      if (Array.isArray(arrEdges)) {
        const drawn = new Set<string>();
        for (const edge of arrEdges) {
          if (typeof edge === 'object' && edge !== null && 'v1' in edge && 'v2' in edge) {
            const fromId = (edge as { v1: string }).v1;
            const toId = (edge as { v2: string }).v2;
            const key = [fromId, toId].sort().join('->');
            if (drawn.has(key)) continue;
            drawn.add(key);
            const fromV = vertexMap.get(fromId);
            const toV = vertexMap.get(toId);
            if (fromV && toV) {
              const color = 0x0000ff;
              const points = [
                new THREE.Vector3(fromV[0], fromV[1], zOffset),
                new THREE.Vector3(toV[0], toV[1], zOffset)
              ];
              const geometry = new THREE.BufferGeometry().setFromPoints(points);
              const material = new THREE.LineBasicMaterial({ color });
              const line = new THREE.Line(geometry, material);
              line.name = 'network';
              scene!.add(line);
            }
          }
        }
      }
    }
    zOffset += FLOOR_GAP;
  }
}

function renderHeatmapLayer() {
  const floorWithGrid = floors.value[currentFloorIdx.value] as { grids?: { origin: number[]; cellSize: number; rows: number; cols: number; cells?: { row: number; col: number; level1?: string; level2?: string; value?: number }[]; data?: number[][]; }[] };
  if (!floorWithGrid?.grids) return;
  const grids = floorWithGrid.grids;
  if (!(Array.isArray(grids) && grids.length > 0 && typeof grids[0] === 'object' && grids[0] !== null)) return;
  const grid = grids[0];
  const { origin, cellSize, rows, cols } = grid;

  // 获取热力图数据：优先使用WebSocket实时数据，否则使用静态数据
  let heatData: number[][] = [];

  if (heatmapData.value && heatmapData.value.map?.building?.floors) {
    // 使用WebSocket实时数据
    const currentFloor = heatmapData.value.map.building.floors[currentFloorIdx.value];
    if (currentFloor?.grids?.[0]?.cells) {
      // 初始化数据数组
      heatData = Array(rows).fill(null).map(() => Array(cols).fill(0));

      // 从cells数据中提取value值
      for (const cell of currentFloor.grids[0].cells) {
        if (cell.row < rows && cell.col < cols && cell.value !== undefined) {
          heatData[cell.row][cell.col] = cell.value;
        }
      }
    }
  } else if (grid.data && Array.isArray(grid.data)) {
    // 使用静态数据
    heatData = grid.data;
  }

  // 热力图模式：每格一个色块，无黑色线框
  if (heatData.length > 0) {
    let maxValue = 0;
    let maxBlockHeight = 0;
    for (const block of floors.value[currentFloorIdx.value].blocks || []) {
      if (typeof block.height === 'number') {
        maxBlockHeight = Math.max(maxBlockHeight, block.height);
      }
    }
    const z = (typeof maxBlockHeight === 'number' ? maxBlockHeight : 0) + 0.2;

    // 计算最大值
    for (let r = 0; r < heatData.length; r++) {
      for (let c = 0; c < heatData[r].length; c++) {
        maxValue = Math.max(maxValue, heatData[r][c]);
      }
    }

    // 渲染热力图格子
    for (let r = 0; r < rows; r++) {
      for (let c = 0; c < cols; c++) {
        const x = origin[0] + c * cellSize;
        const y = origin[1] + r * cellSize;
        const shape = new THREE.Shape();
        shape.moveTo(x, y);
        shape.lineTo(x + cellSize, y);
        shape.lineTo(x + cellSize, y + cellSize);
        shape.lineTo(x, y + cellSize);
        shape.lineTo(x, y);

        const v = (r < heatData.length && c < heatData[r].length) ? heatData[r][c] : 0;
        const ratio = maxValue === 0 ? 0 : v / maxValue;
        const rCol = Math.round(255 * ratio);
        const gCol = 0;
        const bCol = Math.round(255 * (1 - ratio));
        const color = (rCol << 16) | (gCol << 8) | bCol;
        // 低密度为透明
        const opacity = ratio === 0 ? 0 : 0.5;
        const material = new THREE.MeshBasicMaterial({ color, transparent: true, opacity });
        const mesh = new THREE.Mesh(new THREE.ShapeGeometry(shape), material);
        mesh.position.z = z;
        mesh.name = 'heatmap';
        scene!.add(mesh);
      }
    }
  }
}

function renderGridLayer() {
  const floorWithGrid = floors.value[currentFloorIdx.value] as { grids?: { origin: number[]; cellSize: number; rows: number; cols: number; cells?: { row: number; col: number; level1?: string; level2?: string; value?: number }[]; data?: number[][]; }[] };
  if (!floorWithGrid?.grids) return;
  const grids = floorWithGrid.grids;
  if (!(Array.isArray(grids) && grids.length > 0 && typeof grids[0] === 'object' && grids[0] !== null)) return;
  const grid = grids[0];
  const { origin, cellSize, rows, cols, cells } = grid;

  // 取block颜色映射
  const blockColorMap = new Map<string, number>();
  let maxBlockHeight = 0;
  for (const block of floors.value[currentFloorIdx.value].blocks || []) {
    blockColorMap.set(block.id, block.rgb);
    if (typeof block.height === 'number') {
      maxBlockHeight = Math.max(maxBlockHeight, block.height);
    }
  }

  // 网格图层Z轴位置，与热力图保持一致
  const gridZ = (typeof maxBlockHeight === 'number' ? maxBlockHeight : 0) + 0.1;
  const borderZ = gridZ + 0.01;

  // 网格图逻辑，保留黑色线框
  for (let r = 0; r < rows; r++) {
    for (let c = 0; c < cols; c++) {
      const x = origin[0] + c * cellSize;
      const y = origin[1] + r * cellSize;
      const shape = new THREE.Shape();
      shape.moveTo(x, y);
      shape.lineTo(x + cellSize, y);
      shape.lineTo(x + cellSize, y + cellSize);
      shape.lineTo(x, y + cellSize);
      shape.lineTo(x, y);
      let color = 0xffffff, opacity = 1.0, showBorder = true;
      if (cells) {
        const cell = cells.find(cell => cell.row === r && cell.col === c);
        if (!cell) {
          opacity = 0.0;
          showBorder = false;
        } else if (cell.level1 === 'nothing') {
          if (cell.level2 && cell.level2 !== null && blockColorMap.has(cell.level2)) {
            // 当热力图可见时，网格统一使用白色；否则使用原始颜色
            color = heatmapVisible.value ? 0xffffff : blockColorMap.get(cell.level2)!;
            opacity = 0.7;
            showBorder = true;
          } else {
            color = 0xffffff;
            opacity = 1.0;
            showBorder = true;
          }
        } else if (cell.level1 && blockColorMap.has(cell.level1)) {
          // 当热力图可见时，网格统一使用白色；否则使用原始颜色
          color = heatmapVisible.value ? 0xffffff : blockColorMap.get(cell.level1)!;
          opacity = 0.7;
          showBorder = true;
        }
      }
      const material = new THREE.MeshBasicMaterial({ color, transparent: true, opacity });
      const mesh = new THREE.Mesh(new THREE.ShapeGeometry(shape), material);
      mesh.position.z = gridZ;
      scene!.add(mesh);
      // 渲染黑色线框
      if (showBorder) {
        const borderGeo = new THREE.BufferGeometry().setFromPoints([
          new THREE.Vector3(x, y, borderZ),
          new THREE.Vector3(x + cellSize, y, borderZ),
          new THREE.Vector3(x + cellSize, y + cellSize, borderZ),
          new THREE.Vector3(x, y + cellSize, borderZ),
          new THREE.Vector3(x, y, borderZ)
        ]);
        const borderMat = new THREE.LineBasicMaterial({ color: 0x000000 });
        const border = new THREE.Line(borderGeo, borderMat);
        scene!.add(border);
      }
    }
  }
}

function onMouseMove(event: MouseEvent) {
  if (!renderer || !camera || !scene) return;
  const rect = renderer.domElement.getBoundingClientRect();
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
  raycaster.setFromCamera(mouse, camera);
  const intersects = raycaster.intersectObjects(vertexMeshes, false);
  if (intersects.length > 0) {
    const mesh = intersects[0].object as THREE.Mesh;
    hoveredVertexId.value = mesh.userData.vertexId;
    hoveredScreen.value = { x: event.clientX, y: event.clientY };
  } else {
    hoveredVertexId.value = null;
  }
  // --- 新增：block拾取 ---
  const blockMeshes = scene.children.filter(obj => obj instanceof THREE.Mesh && obj.userData.vertexName);
  const blockIntersects = raycaster.intersectObjects(blockMeshes as THREE.Object3D[], false);
  if (blockIntersects.length > 0) {
    const mesh = blockIntersects[0].object as THREE.Mesh;
    hoveredBlockName.value = mesh.userData.vertexName;
    hoveredBlockScreen.value = { x: event.clientX, y: event.clientY };
  } else {
    hoveredBlockName.value = null;
  }
  // ---
}

function removeHeatmapMeshes() {
  if (scene) {
    scene.children = scene.children.filter(obj => !(obj instanceof THREE.Mesh && obj.name === 'heatmap'));
  }
}

// 独立更新热力图，不重新渲染整个地图
function updateHeatmapOnly() {
  if (!heatmapVisible.value || !scene) return;

  // 移除现有热力图网格
  removeHeatmapMeshes();

  // 重新渲染热力图
  renderHeatmapLayer();
}

function removeNetworkMeshes() {
  if (scene) {
    scene.children = scene.children.filter(obj => !(obj.name === 'network'));
  }
}

function setTopView() {
  if (!camera || !controls) return;
  const { centerX, centerY, minX, maxX, minY, maxY } = getGlobalCenter();
  const mapWidth = maxX - minX;
  const mapHeight = maxY - minY;
  const mapSize = Math.max(mapWidth, mapHeight);
  if (!isTopView.value) {
    // 保存原始视角
    originalCameraPos.value = { x: camera.position.x, y: camera.position.y, z: camera.position.z };
    originalTarget.value = { x: controls.target.x, y: controls.target.y, z: controls.target.z };
    // 切换为俯视
    camera.position.set(centerX, centerY, mapSize * 2);
    camera.lookAt(centerX, centerY, 0);
    controls.target.set(centerX, centerY, 0);
    controls.update();
    isTopView.value = true;
  } else {
    // 恢复原始视角
    if (originalCameraPos.value && originalTarget.value) {
      camera.position.set(originalCameraPos.value.x, originalCameraPos.value.y, originalCameraPos.value.z);
      controls.target.set(originalTarget.value.x, originalTarget.value.y, originalTarget.value.z);
      camera.lookAt(originalTarget.value.x, originalTarget.value.y, originalTarget.value.z);
      controls.update();
    }
    isTopView.value = false;
  }
}

// 通过axios获取后端json数据
// @ts-ignore: 保留此函数以备将来使用
async function fetchMapJson(fileName: string) {
  try {
    // 假设后端接口为 /api/maps/，fileName 例如 'map_精简版.json'
    const response = await axios.get(`/api/maps/${fileName}`);
    return response.data;
  } catch (error) {
    console.error('获取地图数据失败:', error);
    return null;
  }
}

onMounted(() => {
  // 只有当mapTitle有值时才发送更新事件
  if (mapTitle.value) {
    emit('update:mapTitle', mapTitle.value);
  }
  drawMap();
  if (threeContainer.value) {
    threeContainer.value.addEventListener('mousemove', onMouseMove);
  }
});

onBeforeUnmount(() => {
  if (animationId) cancelAnimationFrame(animationId);
  if (renderer && renderer.domElement && renderer.domElement.parentNode) {
    renderer.domElement.parentNode.removeChild(renderer.domElement);
  }
  renderer = null;
  scene = null;
  camera = null;
  controls = null;
  if (threeContainer.value) {
    threeContainer.value.removeEventListener('mousemove', onMouseMove);
  }
  // 清理WebSocket连接
  disconnectWebSocket();
});
</script>

<style scoped>
/* 面板通用样式 */
.panel-section {
  margin-bottom: 5px;
  border: 1px solid #1e3a5f;
  border-radius: 4px;
  background-color: #0d2b55;
}

.map-title {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: bold;
  color: white;
}

/* 地图容器样式 */
.map-content {
  position: relative;
  height: 400px;
  padding: 8px;
  overflow: hidden;
}

.map-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.map-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.map-btn:hover {
  background-color: #40a9ff;
}

.map-btn.active {
  background-color: #52c41a;
}

.map-btn.only {
  background-color: #a259ff;
}

/* WebSocket状态指示器样式 */
.ws-status {
  margin-left: 5px;
  font-size: 12px;
  color: #ff6b6b;
}

.ws-status.connected {
  color: #51cf66;
}

.map-viewport {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.three-map-canvas {
  width: 100%;
  height: 100%;
  background: #0d2b55;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

/* 地图选择器样式 */
.map-selector {
  position: absolute;
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  pointer-events: auto;
}
.map-select {
  padding: 6px 10px;
  background: #fff;
  border: 1px solid #3553b0;
  border-radius: 4px;
  font-size: 12px;
  color: #3553b0;
  cursor: pointer;
  outline: none;
  transition: all 0.2s;
}
.map-select:hover {
  background: #f0f4ff;
  border-color: #409eff;
}
.map-select:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.map-right-controls {
  position: absolute;
  top: 50px;
  right: 10px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.mode-control {
  text-align: center;
  margin-bottom: 15px;
}

.mode-btn {
  width: 30px;
  height: 30px;
  background-color: #ffffff;
  border: 1px solid #ccc;
  border-radius: 1px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
  color: #333;
  font-size: 0.7rem;
}

.mode-btn:hover {
  transform: scale(1.1);
}

.icon-3d {
  font-size: 10px;
  font-weight: bold;
}

.floor-controls {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.floor-btn {
  width: 30px;
  height: 30px;
  background-color: #ffffff;
  border: 1px solid #ccc;
  border-radius: 0.5px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
  font-size: 0.5rem;
  font-weight: bold;
  color: #333;
}

.floor-btn:hover {
  transform: scale(1.1);
}

.floor-btn:active {
  transform: scale(0.9);
  background-color: #1e82fa;
}

.floor-btn.active {
  color: #2997e3;
}

.floor-icon-btn {
  padding: 0;
  background: #ffffff;
}

.floor-icon {
  font-size: 8px;
  font-weight: bold;
}

.zoom-controls {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.zoom-btn {
  width: 30px;
  height: 30px;
  background-color: #ffffff;
  border: 0.5px solid #ccc;
  border-radius: 1px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
  font-size: 0.7rem;
}

.zoom-btn:hover {
  transform: scale(1.1);
}

.zoom-btn:active {
  transform: scale(0.9);
  background-color: #1e82fa;
}

.zoom-btn::before {
  content: '+';
  font-size: 20px;
  color: #333;
}

.zoom-btn.zoom-out::before {
  content: '-';
}
.map-scale {
  position: absolute;
  left: 16px;
  bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 2;
  pointer-events: auto;
}
.scale-bar-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  height: 16px;
}
.scale-bar {
  position: absolute;
  left: 0;
  top: 8px;
  height: 4px;
  background: #222;
  border-radius: 2px;
}
.scale-bar-tip {
  position: absolute;
  top: 0;
  width: 2px;
  height: 8px;
  background: #222;
  border-radius: 1px;
  z-index: 1;
}
.scale-label {
  color: #222;
  font-size: 14px;
  font-weight: bold;
  background: #fff;
  border-radius: 4px;
  padding: 2px 8px;
}

.compass {
  position: absolute;
  right: 16px;
  bottom: 16px;
  width: 36px;
  height: 36px;
  z-index: 10;
  transition: transform 0.2s;
  user-select: none;
}
</style>
